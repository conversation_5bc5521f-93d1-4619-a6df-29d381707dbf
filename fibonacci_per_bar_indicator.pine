//@version=5
indicator("Combined Fibonacci & MTF Candle Indicator", overlay=true, max_lines_count=500, max_labels_count=500)

// Input parameters
show_levels = input.bool(true, "Show Fibonacci Levels", group="Display Settings")
show_labels = input.bool(true, "Show Level Labels", group="Display Settings")
show_trendlines = input.bool(true, "Show Trend Lines", group="Display Settings")
max_bars_back = input.int(50, "Maximum Bars to Display", minval=1, maxval=100, group="Display Settings")
line_width = input.int(1, "Line Width", minval=1, maxval=3, group="Display Settings")
trendline_width = input.int(2, "Trend Line Width", minval=1, maxval=5, group="Display Settings")
transparency = input.int(20, "Line Transparency", minval=0, maxval=100, group="Display Settings")

// Multi-timeframe mode selection
use_mtf_mode = input.bool(false, "Use Multi-Timeframe Mode", group="Mode Settings", tooltip="Enable to use MTF candle logic for Fibonacci drawing. When enabled, Fibs are drawn based on MTF candle OHLC instead of bar high/low.")
mtf_timeframe = input.timeframe("5", "MTF Timeframe", group="Mode Settings", tooltip="Timeframe for MTF mode. Fibonacci levels will be calculated from MTF candle open/close range.")
use_confirmed_data = input.bool(true, "Use Confirmed MTF Data (No Repaint)", group="Mode Settings", tooltip="True = No repainting but 1 candle delay. False = Real-time but repaints.")

// Legacy higher timeframe setting (for backward compatibility)
use_higher_timeframe = input.bool(false, "Use Higher Timeframe Range (Legacy)", group="Legacy Settings", tooltip="Legacy mode: Enable to draw Fibonacci levels based on higher timeframe high/low.")
higher_timeframe = input.timeframe("5", "Higher Timeframe (Legacy)", group="Legacy Settings", tooltip="Legacy mode: Select the timeframe to get high/low from.")

// Trading hours background settings
show_trading_hours_bg = input.bool(true, "Show Trading Hours Background", group="Trading Hours")
trading_hours_session = input.session("0930-1600", "Trading Hours", group="Trading Hours")
timezone_input = input.string("America/New_York", "Timezone", options=["America/New_York", "America/Chicago", "America/Denver", "America/Los_Angeles", "Europe/London", "Europe/Berlin", "Asia/Tokyo", "Asia/Shanghai", "Asia/Kolkata", "Australia/Sydney"], group="Trading Hours")
outside_hours_color = input.color(color.new(color.red, 80), "Outside Trading Hours Color", group="Trading Hours")

// Color inputs
fib_color_1_1 = input.color(color.white, "1.1 Level Color", group="Colors")
fib_color_1_08 = input.color(color.white, "1.08 Level Color", group="Colors")
fib_color_1_0 = input.color(color.green, "1.0 Level Color", group="Colors")
fib_color_0_0 = input.color(color.red, "0.0 Level Color", group="Colors")
fib_color_neg_08 = input.color(color.white, "-0.08 Level Color", group="Colors")
fib_color_neg_1 = input.color(color.white, "-0.1 Level Color", group="Colors")
text_color = input.color(color.white, "Label Text Color", group="Colors")
trendline_color = input.color(color.white, "Trend Line Color", group="Colors")

// Fibonacci levels
fib_levels = array.from(1.1, 1.08, 1.0, 0.0, -0.08, -0.1)
fib_colors = array.from(fib_color_1_1, fib_color_1_08, fib_color_1_0, fib_color_0_0, fib_color_neg_08, fib_color_neg_1)

// Arrays to store lines and labels for cleanup
var lines_array = array.new<line>()
var labels_array = array.new<label>()

// Signal settingsUndeclared identifier 'label'
show_signals = input.bool(true, "Show Buy/Sell Signals", group="Signal Settings")
show_immediate_signals = input.bool(true, "Show Immediate Signals", group="Signal Settings", tooltip="Generate signals when current bar hits green/red lines")
show_delayed_signals = input.bool(true, "Show Delayed Signals", group="Signal Settings", tooltip="Generate signals when next candle after HTF close hits previous green/red lines")
signal_text_size = input.string("small", "Signal Text Size", options=["tiny", "small", "normal", "large"], group="Signal Settings")

// Variables to store current and previous Fibonacci levels for signals
var float current_green_level = na
var float current_red_level = na
var float prev_green_level = na
var float prev_red_level = na
var bool htf_just_closed = false
var int htf_close_bar = na

// Variables to store the newest/most recent Fibonacci levels for signals
var float newest_green_level = na
var float newest_red_level = na

// Variables to track which signals have been triggered to prevent duplicates
var bool green_signal_triggered = false
var bool red_signal_triggered = false

// Variables to track active positions and take profit levels
var bool active_buy_position = false
var bool active_sell_position = false
var float buy_tp_level = na
var float sell_tp_level = na

// Arrays to store signal labels for cleanup
var signal_labels_array = array.new<label>()

// MTF data retrieval (similar to custom candle builder)
// Method 1: Non-repainting (shows completed candles only)
mtf_open_confirmed = use_mtf_mode ? request.security(syminfo.tickerid, mtf_timeframe, open[1], lookahead=barmerge.lookahead_off) : na
mtf_high_confirmed = use_mtf_mode ? request.security(syminfo.tickerid, mtf_timeframe, high[1], lookahead=barmerge.lookahead_off) : na
mtf_low_confirmed = use_mtf_mode ? request.security(syminfo.tickerid, mtf_timeframe, low[1], lookahead=barmerge.lookahead_off) : na
mtf_close_confirmed = use_mtf_mode ? request.security(syminfo.tickerid, mtf_timeframe, close[1], lookahead=barmerge.lookahead_off) : na
mtf_time_confirmed = use_mtf_mode ? request.security(syminfo.tickerid, mtf_timeframe, time[1], lookahead=barmerge.lookahead_off) : na

// Method 2: Real-time (repaints but shows current developing candle)
mtf_open_realtime = use_mtf_mode ? request.security(syminfo.tickerid, mtf_timeframe, open, lookahead=barmerge.lookahead_off) : na
mtf_high_realtime = use_mtf_mode ? request.security(syminfo.tickerid, mtf_timeframe, high, lookahead=barmerge.lookahead_off) : na
mtf_low_realtime = use_mtf_mode ? request.security(syminfo.tickerid, mtf_timeframe, low, lookahead=barmerge.lookahead_off) : na
mtf_close_realtime = use_mtf_mode ? request.security(syminfo.tickerid, mtf_timeframe, close, lookahead=barmerge.lookahead_off) : na
mtf_time_realtime = use_mtf_mode ? request.security(syminfo.tickerid, mtf_timeframe, time, lookahead=barmerge.lookahead_off) : na

// Choose which MTF method to use
mtf_open = use_mtf_mode ? (use_confirmed_data ? mtf_open_confirmed : mtf_open_realtime) : na
mtf_high = use_mtf_mode ? (use_confirmed_data ? mtf_high_confirmed : mtf_high_realtime) : na
mtf_low = use_mtf_mode ? (use_confirmed_data ? mtf_low_confirmed : mtf_low_realtime) : na
mtf_close = use_mtf_mode ? (use_confirmed_data ? mtf_close_confirmed : mtf_close_realtime) : na
mtf_time = use_mtf_mode ? (use_confirmed_data ? mtf_time_confirmed : mtf_time_realtime) : na

// Detect if we're in historical mode vs live/backtest mode
// In historical mode, barstate.ishistory is true for all bars
// In live/backtest mode, barstate.ishistory is false for recent bars
is_historical_mode = barstate.ishistory

// MTF data for signals - choose appropriate offset based on mode
// Historical mode: use no offset (most recent completed)
// Live/backtest mode: use [1] offset (previous completed)
mtf_open_for_signals = use_mtf_mode ? (is_historical_mode ? request.security(syminfo.tickerid, mtf_timeframe, open, lookahead=barmerge.lookahead_off) : request.security(syminfo.tickerid, mtf_timeframe, open[1], lookahead=barmerge.lookahead_off)) : na
mtf_high_for_signals = use_mtf_mode ? (is_historical_mode ? request.security(syminfo.tickerid, mtf_timeframe, high, lookahead=barmerge.lookahead_off) : request.security(syminfo.tickerid, mtf_timeframe, high[1], lookahead=barmerge.lookahead_off)) : na
mtf_low_for_signals = use_mtf_mode ? (is_historical_mode ? request.security(syminfo.tickerid, mtf_timeframe, low, lookahead=barmerge.lookahead_off) : request.security(syminfo.tickerid, mtf_timeframe, low[1], lookahead=barmerge.lookahead_off)) : na
mtf_close_for_signals = use_mtf_mode ? (is_historical_mode ? request.security(syminfo.tickerid, mtf_timeframe, close, lookahead=barmerge.lookahead_off) : request.security(syminfo.tickerid, mtf_timeframe, close[1], lookahead=barmerge.lookahead_off)) : na
mtf_time_for_signals = use_mtf_mode ? (is_historical_mode ? request.security(syminfo.tickerid, mtf_timeframe, time, lookahead=barmerge.lookahead_off) : request.security(syminfo.tickerid, mtf_timeframe, time[1], lookahead=barmerge.lookahead_off)) : na

// Detect new MTF period
is_new_mtf_period = use_mtf_mode ? ta.change(mtf_time) != 0 : false

// Detect new MTF period for signals (using most recent data)
is_new_mtf_period_for_signals = use_mtf_mode ? ta.change(mtf_time_for_signals) != 0 : false

// Calculate timeframe durations (shared for both MTF and HTF modes)
current_tf_duration_ms = timeframe.in_seconds(timeframe.period) * 1000
mtf_duration_ms = use_mtf_mode ? timeframe.in_seconds(mtf_timeframe) * 1000 : 0
bars_in_mtf = use_mtf_mode ? math.round(mtf_duration_ms / current_tf_duration_ms) : 1

// Trading hours detection
is_trading_hours = time(timeframe.period, trading_hours_session, timezone_input)
is_outside_trading_hours = na(is_trading_hours)

// Function to clean up old lines and labels
cleanup_old_objects() =>
    // Clean up lines (6 fib levels + 2 trend lines per bar)
    while array.size(lines_array) > max_bars_back * 8
        old_line = array.shift(lines_array)
        if not na(old_line)
            line.delete(old_line)

    // Clean up labels
    while array.size(labels_array) > max_bars_back * 6  // 6 levels per bar
        old_label = array.shift(labels_array)
        if not na(old_label)
            label.delete(old_label)

    // Clean up signal labels
    while array.size(signal_labels_array) > max_bars_back * 2  // Max 2 signals per bar
        old_signal_label = array.shift(signal_labels_array)
        if not na(old_signal_label)
            label.delete(old_signal_label)

// Function to create signal arrow
create_signal_dot(signal_type, price_level, is_delayed = false) =>
    signal_color = signal_type == "BUY" ? color.green : signal_type == "SELL" ? color.red : color.white

    // Position arrow to the left of the candle horizontally
    // Use the actual signal price level for vertical positioning
    arrow_x_position = bar_index - 1  // Position 1 bar to the left

    // Create arrow pointing right toward the candle
    arrow_text = signal_type == "TP" ? "▶" : "▶"  // White arrow for take profit, colored for entry signals
    signal_label = label.new(x=arrow_x_position, y=price_level, text=arrow_text, style=label.style_none, color=color.new(color.white, 100), textcolor=signal_color, size=size.normal, xloc=xloc.bar_index)
    array.push(signal_labels_array, signal_label)

    // Print signal to console
    signal_desc = signal_type + (is_delayed ? " (Delayed)" : "")
    log.info(signal_desc + " signal at " + str.tostring(price_level, "#.###") + " on bar " + str.tostring(bar_index))

// Function to check if signal conditions are met (returns signal info)
get_signal_conditions(green_level, red_level) =>
    buy_signal = show_signals and show_immediate_signals and not na(green_level) and not na(red_level) and high >= green_level and low <= green_level and not green_signal_triggered
    sell_signal = show_signals and show_immediate_signals and not na(green_level) and not na(red_level) and low <= red_level and high >= red_level and not red_signal_triggered
    [buy_signal, sell_signal]

// Function to calculate take profit levels based on current fibonacci levels
calculate_tp_levels(range_low, price_range) =>
    tp_buy_level = range_low + (price_range * 1.1)   // 1.1 level for buy take profit
    tp_sell_level = range_low + (price_range * -0.1) // -0.1 level for sell take profit
    [tp_buy_level, tp_sell_level]

// Function to check for take profit conditions
check_take_profit_conditions() =>
    // For buy TP: trigger when price reaches or exceeds the TP level (1.1 level)
    buy_tp_hit = active_buy_position and not na(buy_tp_level) and high >= buy_tp_level
    // For sell TP: trigger when price reaches or goes below the TP level (-0.1 level)
    sell_tp_hit = active_sell_position and not na(sell_tp_level) and low <= sell_tp_level
    [buy_tp_hit, sell_tp_hit]



// Get higher timeframe data if enabled
htf_high = use_higher_timeframe ? request.security(syminfo.tickerid, higher_timeframe, high, lookahead=barmerge.lookahead_off) : na
htf_low = use_higher_timeframe ? request.security(syminfo.tickerid, higher_timeframe, low, lookahead=barmerge.lookahead_off) : na

// Detect new higher timeframe period and calculate timeframe duration
htf_time = use_higher_timeframe ? request.security(syminfo.tickerid, higher_timeframe, time, lookahead=barmerge.lookahead_off) : na
is_new_htf_period = use_higher_timeframe ? ta.change(htf_time) != 0 : false

// Calculate the number of bars in the higher timeframe period
htf_duration_ms = timeframe.in_seconds(higher_timeframe) * 1000
bars_in_htf = use_higher_timeframe ? math.round(htf_duration_ms / current_tf_duration_ms) : 1

// Calculate newest Fibonacci levels for signals using most recent MTF data
if use_mtf_mode and is_new_mtf_period_for_signals and
   not na(mtf_open_for_signals) and not na(mtf_high_for_signals) and not na(mtf_low_for_signals) and not na(mtf_close_for_signals)

    // Calculate newest fib levels using most recent MTF data
    newest_range_high = mtf_high_for_signals
    newest_range_low = mtf_low_for_signals
    newest_price_range = newest_range_high - newest_range_low

    if newest_price_range > 0
        newest_green_level := newest_range_low + (newest_price_range * 1.0)  // 100% level
        newest_red_level := newest_range_low + (newest_price_range * 0.0)    // 0% level

// Check for delayed signals using the NEWEST fib levels available
if show_signals and show_delayed_signals and htf_just_closed
    // Use the newest fib levels for delayed signals to prevent repainting
    signal_green = not na(newest_green_level) ? newest_green_level : (not na(current_green_level) ? current_green_level : prev_green_level)
    signal_red = not na(newest_red_level) ? newest_red_level : (not na(current_red_level) ? current_red_level : prev_red_level)

    if not na(signal_green) and not na(signal_red)
        // Check if current bar hits newest green level (buy signal) and signal not already triggered
        if high >= signal_green and low <= signal_green and not green_signal_triggered
            create_signal_dot("BUY", signal_green, true)
            green_signal_triggered := true
            htf_just_closed := false  // Reset flag after signal
            // Update position tracking for buy signal
            active_buy_position := true
            active_sell_position := false
            sell_tp_level := na

        // Check if current bar hits newest red level (sell signal) and signal not already triggered
        if low <= signal_red and high >= signal_red and not red_signal_triggered
            create_signal_dot("SELL", signal_red, true)
            red_signal_triggered := true
            htf_just_closed := false  // Reset flag after signal
            // Update position tracking for sell signal
            active_sell_position := true
            active_buy_position := false
            buy_tp_level := na

// Check for take profit conditions on every bar (outside of specific mode logic)
if barstate.isconfirmed and (active_buy_position or active_sell_position)
    [buy_tp_hit, sell_tp_hit] = check_take_profit_conditions()
    if buy_tp_hit
        create_signal_dot("TP", buy_tp_level, false)
        // Close positions when take profit is hit
        active_buy_position := false
        active_sell_position := false
        buy_tp_level := na
        sell_tp_level := na
    if sell_tp_hit
        create_signal_dot("TP", sell_tp_level, false)
        // Close positions when take profit is hit
        active_buy_position := false
        active_sell_position := false
        buy_tp_level := na
        sell_tp_level := na

// Main execution logic - choose between MTF mode and legacy mode
if show_levels and (not show_trading_hours_bg or not is_outside_trading_hours)

    // MTF MODE: Use MTF candle logic for Fibonacci drawing
    if use_mtf_mode
        // Additional condition to prevent repainting on real-time bars
        draw_condition = use_confirmed_data ? true : barstate.isconfirmed

        // Draw Fibonacci levels when new MTF period starts
        if (draw_condition and is_new_mtf_period and
           not na(mtf_open) and not na(mtf_high) and not na(mtf_low) and not na(mtf_close))

            // Store previous levels for delayed signals before calculating new ones
            if not na(current_green_level) and not na(current_red_level)
                prev_green_level := current_green_level
                prev_red_level := current_red_level
                htf_just_closed := true
                htf_close_bar := bar_index

            // Use MTF candle's high/low as the range for 0 and 1 levels
            // This anchors the key Fibonacci levels at the actual high/low extremes
            range_high = mtf_high
            range_low = mtf_low
            price_range = range_high - range_low

            if price_range > 0
                // Calculate NEW current green (1.0) and red (0.0) levels
                new_green_level = range_low + (price_range * 1.0)  // 100% level
                new_red_level = range_low + (price_range * 0.0)    // 0% level

                // Update current levels with the newest calculations
                current_green_level := new_green_level
                current_red_level := new_red_level

                // Reset signal flags for new Fibonacci levels
                green_signal_triggered := false
                red_signal_triggered := false

                // Get the MTF start time for time-based positioning
                mtf_start_time = use_confirmed_data ? mtf_time_confirmed : mtf_time_realtime
                mtf_duration_ms := timeframe.in_seconds(mtf_timeframe) * 1000
                mtf_end_time = mtf_start_time + mtf_duration_ms

                // Draw trend line spanning the MTF period
                if show_trendlines
                    // Determine if MTF candle is bullish or bearish
                    is_mtf_bullish = mtf_close > mtf_open

                    // Set trend line direction based on candle direction
                    trend_start_y = is_mtf_bullish ? range_low : range_high
                    trend_end_y = is_mtf_bullish ? range_high : range_low

                    trend_line_main = line.new(x1=mtf_start_time, y1=trend_start_y, x2=mtf_end_time, y2=trend_end_y,
                                             color=color.new(trendline_color, 50), width=trendline_width,
                                             style=line.style_dashed, xloc=xloc.bar_time)
                    array.push(lines_array, trend_line_main)

                // Draw Fibonacci levels as horizontal lines spanning the MTF period
                for i = 0 to array.size(fib_levels) - 1
                    fib_level = array.get(fib_levels, i)
                    fib_price = range_low + (price_range * fib_level)
                    fib_color = array.get(fib_colors, i)

                    new_line = line.new(x1=mtf_start_time, y1=fib_price, x2=mtf_end_time, y2=fib_price,
                                       color=color.new(fib_color, transparency), width=line_width,
                                       style=line.style_solid, xloc=xloc.bar_time)
                    array.push(lines_array, new_line)

                    if show_labels
                        label_text = str.tostring(fib_level, "#.##") + " (" + str.tostring(fib_price, "#.###") + ") [MTF " + mtf_timeframe + "]"
                        new_label = label.new(x=mtf_start_time, y=fib_price, text=label_text,
                                             style=label.style_label_right, color=color.new(color.black, 100),
                                             textcolor=text_color, size=size.small, xloc=xloc.bar_time)
                        array.push(labels_array, new_label)

                // Calculate take profit levels for new fibonacci levels
                [tp_buy, tp_sell] = calculate_tp_levels(range_low, price_range)

                // Update take profit levels for active positions
                if active_buy_position
                    buy_tp_level := tp_buy
                if active_sell_position
                    sell_tp_level := tp_sell

                // Check for immediate signals with newest levels
                signal_green_immediate = not na(newest_green_level) ? newest_green_level : current_green_level
                signal_red_immediate = not na(newest_red_level) ? newest_red_level : current_red_level
                [buy_condition, sell_condition] = get_signal_conditions(signal_green_immediate, signal_red_immediate)
                if buy_condition
                    create_signal_dot("BUY", signal_green_immediate, false)
                    green_signal_triggered := true
                    // Update position tracking and set take profit level for new buy position
                    active_buy_position := true
                    active_sell_position := false
                    buy_tp_level := tp_buy
                    sell_tp_level := na
                if sell_condition
                    create_signal_dot("SELL", signal_red_immediate, false)
                    red_signal_triggered := true
                    // Update position tracking and set take profit level for new sell position
                    active_sell_position := true
                    active_buy_position := false
                    sell_tp_level := tp_sell
                    buy_tp_level := na

                // Clean up old objects
                cleanup_old_objects()

        // Check for immediate signals on every bar using newest available levels (MTF mode)
        else if use_mtf_mode and barstate.isconfirmed
            signal_green_bar = not na(newest_green_level) ? newest_green_level : current_green_level
            signal_red_bar = not na(newest_red_level) ? newest_red_level : current_red_level

            // Check for take profit conditions first
            [buy_tp_hit, sell_tp_hit] = check_take_profit_conditions()
            if buy_tp_hit
                create_signal_dot("TP", buy_tp_level, false)
                // Close positions when take profit is hit
                active_buy_position := false
                active_sell_position := false
                buy_tp_level := na
                sell_tp_level := na
            if sell_tp_hit
                create_signal_dot("TP", sell_tp_level, false)
                // Close positions when take profit is hit
                active_buy_position := false
                active_sell_position := false
                buy_tp_level := na
                sell_tp_level := na

            if not na(signal_green_bar) and not na(signal_red_bar)
                [buy_condition, sell_condition] = get_signal_conditions(signal_green_bar, signal_red_bar)
                if buy_condition
                    create_signal_dot("BUY", signal_green_bar, false)
                    green_signal_triggered := true
                    // Update position tracking for buy signal
                    active_buy_position := true
                    active_sell_position := false
                    sell_tp_level := na
                if sell_condition
                    create_signal_dot("SELL", signal_red_bar, false)
                    red_signal_triggered := true
                    // Update position tracking for sell signal
                    active_sell_position := true
                    active_buy_position := false
                    buy_tp_level := na

    // LEGACY MODE: Original per-bar or higher timeframe logic
    else if barstate.isconfirmed
        // Determine which high/low to use
        current_high = use_higher_timeframe and not na(htf_high) ? htf_high : high
        current_low = use_higher_timeframe and not na(htf_low) ? htf_low : low
        price_range = current_high - current_low

        if price_range > 0
            // Calculate current green (1.0) and red (0.0) levels for legacy mode
            current_green_level := current_low + (price_range * 1.0)  // 100% level
            current_red_level := current_low + (price_range * 0.0)    // 0% level

            // Store previous levels when new HTF period starts
            if use_higher_timeframe and is_new_htf_period
                if not na(prev_green_level) and not na(prev_red_level)
                    htf_just_closed := true
                    htf_close_bar := bar_index
                prev_green_level := current_green_level
                prev_red_level := current_red_level
                // Reset signal flags for new Fibonacci levels
                green_signal_triggered := false
                red_signal_triggered := false
            else if not use_higher_timeframe
                // For non-HTF mode, reset flags each bar to allow new signals
                green_signal_triggered := false
                red_signal_triggered := false
            // Calculate offset based on higher timeframe duration
            bar_offset = use_higher_timeframe ? bars_in_htf : 0

            // Draw trend line only when new higher timeframe period starts (or every bar if not using HTF)
            if show_trendlines and (not use_higher_timeframe or is_new_htf_period)
                trend_line_main = line(na)
                if use_higher_timeframe
                    // Trend line spanning exactly the HTF period from low to high
                    trend_line_main := line.new(x1=bar_index - bar_offset + 1, y1=current_low, x2=bar_index, y2=current_high,
                                               color=color.new(trendline_color, 50), width=trendline_width,
                                               style=line.style_dashed, xloc=xloc.bar_index)
                else
                    // Single bar trend line when not using HTF
                    trend_line_main := line.new(x1=bar_index, y1=current_low, x2=bar_index + 1, y2=current_high,
                                               color=color.new(trendline_color, 50), width=trendline_width,
                                               style=line.style_dashed, xloc=xloc.bar_index)
                array.push(lines_array, trend_line_main)

            // Draw Fibonacci levels only when new HTF period starts (or every bar if not using HTF)
            if show_levels and (not use_higher_timeframe or is_new_htf_period)
                for i = 0 to array.size(fib_levels) - 1
                    fib_level = array.get(fib_levels, i)
                    fib_price = current_low + (price_range * fib_level)
                    fib_color = array.get(fib_colors, i)

                    // Create horizontal line spanning exactly the HTF period duration
                    line_start_x = use_higher_timeframe ? bar_index - bar_offset + 1 : bar_index
                    line_end_x = use_higher_timeframe ? bar_index : bar_index + 1

                    new_line = line.new(x1=line_start_x, y1=fib_price, x2=line_end_x, y2=fib_price,
                                       color=color.new(fib_color, transparency), width=line_width,
                                       style=line.style_solid, xloc=xloc.bar_index)
                    array.push(lines_array, new_line)

                    if show_labels
                        // Format label to show both fib level and price
                        timeframe_suffix = use_higher_timeframe ? " [" + higher_timeframe + "]" : ""
                        label_text = str.tostring(fib_level, "#.##") + " (" + str.tostring(fib_price, "#.###") + ")" + timeframe_suffix
                        label_x = use_higher_timeframe ? bar_index - bar_offset + 1 : bar_index
                        new_label = label.new(x=label_x, y=fib_price, text=label_text,
                                             style=label.style_label_right, color=color.new(color.black, 100),
                                             textcolor=text_color, size=size.small, xloc=xloc.bar_index)
                        array.push(labels_array, new_label)

            // Calculate and update take profit levels for legacy mode
            [tp_buy, tp_sell] = calculate_tp_levels(current_low, price_range)
            if active_buy_position
                buy_tp_level := tp_buy
            if active_sell_position
                sell_tp_level := tp_sell

            // Check for take profit conditions first
            [buy_tp_hit, sell_tp_hit] = check_take_profit_conditions()
            if buy_tp_hit
                create_signal_dot("TP", buy_tp_level, false)
                // Close positions when take profit is hit
                active_buy_position := false
                active_sell_position := false
                buy_tp_level := na
                sell_tp_level := na
            if sell_tp_hit
                create_signal_dot("TP", sell_tp_level, false)
                // Close positions when take profit is hit
                active_buy_position := false
                active_sell_position := false
                buy_tp_level := na
                sell_tp_level := na

            // Check for immediate signals with current levels (for all bars in legacy mode)
            [buy_condition, sell_condition] = get_signal_conditions(current_green_level, current_red_level)
            if buy_condition
                create_signal_dot("BUY", current_green_level, false)
                green_signal_triggered := true
                // Update position tracking and set take profit level for new buy position
                active_buy_position := true
                active_sell_position := false
                buy_tp_level := tp_buy
                sell_tp_level := na
            if sell_condition
                create_signal_dot("SELL", current_red_level, false)
                red_signal_triggered := true
                // Update position tracking and set take profit level for new sell position
                active_sell_position := true
                active_buy_position := false
                sell_tp_level := tp_sell
                buy_tp_level := na

            // Clean up old objects to maintain performance
            cleanup_old_objects()

// Plot high and low of current bar for reference (invisible)
plot(high, color=color.new(color.lime, 100), linewidth=1, title="Bar High")
plot(low, color=color.new(color.red, 100), linewidth=1, title="Bar Low")

// Background color for outside trading hours
bgcolor(show_trading_hours_bg and is_outside_trading_hours ? outside_hours_color : na, title="Outside Trading Hours Background")
